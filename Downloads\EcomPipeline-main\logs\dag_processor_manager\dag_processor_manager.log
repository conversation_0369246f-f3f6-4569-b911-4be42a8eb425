[2025-08-16T23:28:00.727+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-16T23:28:00.728+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-16T23:28:00.728+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-16T23:28:00.728+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:28:00.730+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-16T23:33:01.331+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:33:01.341+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-16T23:38:02.042+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:38:02.045+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-16T23:43:02.387+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:43:02.393+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-16T23:48:02.732+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:48:02.741+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-16T23:53:03.061+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:53:03.070+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-16T23:58:03.394+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-16T23:58:03.400+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T00:03:03.695+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T00:03:03.703+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T00:48:26.476+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-17T00:48:26.512+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-17T00:48:26.516+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-17T00:48:26.517+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T00:48:26.521+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T00:53:26.732+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T00:53:26.740+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T00:58:27.084+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T00:58:27.087+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:03:27.432+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:03:27.432+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:08:27.759+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:08:27.763+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:13:28.075+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:13:28.076+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:18:28.317+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:18:28.317+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:23:28.559+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:23:28.559+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:28:28.814+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:28:28.814+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:33:29.033+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:33:29.061+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:38:29.375+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:38:29.377+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T01:43:29.612+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T01:43:29.613+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T20:49:56.466+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-17T20:49:56.501+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-17T20:49:56.502+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-17T20:49:56.502+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T20:49:56.503+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T20:54:56.667+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T20:54:56.672+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T20:59:56.950+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T20:59:56.952+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T21:04:57.176+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T21:04:57.180+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T21:09:57.419+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T21:09:57.420+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-17T21:14:57.627+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-17T21:14:57.628+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T12:39:29.588+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T12:39:29.646+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T12:39:29.646+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T12:39:29.647+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T12:39:29.648+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T12:44:30.429+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T12:44:30.435+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T12:49:30.804+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T12:49:30.811+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T12:54:31.170+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T12:54:31.171+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T12:59:31.453+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T12:59:31.456+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T13:04:31.760+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T13:04:31.762+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T13:09:32.094+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T13:09:32.094+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:09:18.168+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T16:09:18.270+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T16:09:18.271+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T16:09:18.271+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:09:18.275+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:14:19.038+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:14:19.043+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:19:19.492+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:19:19.493+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:24:19.825+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:24:19.846+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:29:20.190+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:29:20.191+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:34:20.536+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:34:20.566+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:39:20.922+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:39:20.922+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T16:44:21.266+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T16:44:21.269+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:05:51.802+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T17:05:51.854+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T17:05:51.854+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T17:05:51.855+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:05:51.856+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:10:52.525+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:10:52.530+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:15:52.966+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:15:52.971+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:20:53.336+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:20:53.338+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:25:53.602+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:25:53.604+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:30:53.897+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:30:53.897+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T17:57:29.246+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T17:57:29.308+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T17:57:29.309+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T17:57:29.309+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T17:57:29.310+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T18:02:29.930+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T18:02:29.943+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T18:07:30.271+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T18:07:30.272+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T18:12:30.588+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T18:12:30.590+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T18:17:30.978+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T18:17:30.978+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T18:22:31.220+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T18:22:31.221+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T19:30:48.826+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T19:30:48.907+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T19:30:48.907+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T19:30:48.907+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T19:30:48.910+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T19:35:49.568+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T19:35:49.575+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T19:40:49.990+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T19:40:50.008+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T19:45:50.408+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T19:45:50.416+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T19:50:50.827+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T19:50:50.828+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T19:55:51.154+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T19:55:51.157+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:00:51.414+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:00:51.415+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:05:51.729+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:05:51.730+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:10:52.045+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:10:52.050+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:15:52.388+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:15:52.389+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:20:52.757+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:20:52.758+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:25:53.033+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:25:53.034+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:30:53.340+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:30:53.344+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:35:53.725+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:35:53.726+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:40:54.036+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:40:54.037+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:45:54.395+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:45:54.396+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:50:54.713+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:50:54.717+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T20:55:55.015+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T20:55:55.015+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:00:55.371+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:00:55.375+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:05:55.729+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:05:55.730+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:10:56.044+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:10:56.045+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:22:04.953+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T21:22:05.175+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T21:22:05.175+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T21:22:05.175+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:22:05.176+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:27:05.521+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:27:05.530+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:32:05.881+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:32:05.887+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:37:06.105+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:37:06.106+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:42:06.433+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:42:06.433+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:47:06.827+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:47:06.831+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T21:52:07.206+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T21:52:07.207+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T23:20:18.503+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-18T23:20:18.516+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-18T23:20:18.517+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-18T23:20:18.517+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T23:20:18.518+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T23:25:18.987+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T23:25:18.994+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T23:30:19.292+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T23:30:19.296+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T23:35:19.599+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T23:35:19.603+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T23:40:19.850+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T23:40:19.851+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-18T23:45:20.169+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-18T23:45:20.186+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:19:40.018+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T11:19:40.147+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T11:19:40.148+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T11:19:40.148+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:19:40.149+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:24:40.792+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:24:40.799+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:29:41.204+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:29:41.228+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:34:41.539+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:34:41.539+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:39:41.835+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:39:41.836+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:44:38.743+0000] {manager.py:469} INFO - Exiting gracefully upon receiving signal 15
[2025-08-19T11:47:13.059+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T11:47:13.075+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T11:47:13.081+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T11:47:13.082+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:47:13.100+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:52:13.237+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:52:13.245+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T11:57:13.536+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T11:57:13.544+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:02:13.771+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:02:13.772+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:07:14.378+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:07:14.387+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:12:14.962+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:12:14.975+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:17:15.344+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:17:15.348+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:22:15.675+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:22:15.770+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:22:56.901+0000] {manager.py:469} INFO - Exiting gracefully upon receiving signal 15
[2025-08-19T12:25:44.151+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T12:25:44.172+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T12:25:44.172+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T12:25:44.173+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:25:44.177+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:30:44.853+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:30:44.856+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:34:56.142+0000] {manager.py:469} INFO - Exiting gracefully upon receiving signal 15
[2025-08-19T12:39:44.992+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T12:39:45.015+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T12:39:45.015+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T12:39:45.015+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:39:45.017+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:44:45.718+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:44:45.889+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:49:46.585+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:49:46.597+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:54:47.314+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:54:47.318+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T12:59:48.270+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T12:59:48.288+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:04:49.174+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:04:49.181+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:09:50.031+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:09:50.039+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:14:50.739+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:14:50.777+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:19:50.975+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:19:51.031+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:24:51.845+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:24:51.857+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:29:52.719+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:29:52.741+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:34:53.344+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:34:53.383+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:39:54.064+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:39:54.090+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:44:55.050+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:44:55.059+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:49:55.896+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:49:55.904+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:54:56.010+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:54:56.038+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T13:59:56.011+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T13:59:56.018+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:04:56.912+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:04:56.920+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:09:57.704+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:09:57.716+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:14:57.818+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:14:57.840+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:19:57.858+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:19:57.865+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:24:58.064+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:24:58.089+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:29:58.875+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:29:58.882+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:34:59.712+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:34:59.783+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:39:59.745+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:39:59.759+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:45:00.720+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:45:00.727+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:50:01.316+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:50:01.389+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T14:59:05.414+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T14:59:05.440+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T14:59:05.440+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T14:59:05.440+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T14:59:05.445+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:04:05.750+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:04:05.772+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:09:06.634+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:09:06.682+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:14:07.390+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:14:07.398+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:19:08.039+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:19:08.045+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:24:08.784+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:24:08.795+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:29:08.987+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:29:09.005+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:34:09.257+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:34:09.319+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:39:09.324+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:39:09.335+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:44:09.687+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:44:09.698+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:49:10.016+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:49:10.017+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:54:10.281+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:54:10.285+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T15:59:10.533+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T15:59:10.534+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:04:10.830+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:04:10.831+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:09:11.113+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:09:11.114+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:14:11.340+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:14:11.340+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:19:11.596+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:19:11.597+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:24:11.815+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:24:11.817+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:29:12.049+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:29:12.051+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:34:12.362+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:34:12.363+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:39:12.587+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:39:12.588+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:44:12.870+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:44:12.871+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:49:13.288+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:49:13.291+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:54:13.542+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:54:13.542+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T16:59:13.788+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T16:59:13.789+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:04:14.091+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:04:14.092+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:30:32.235+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T17:30:32.367+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T17:30:32.367+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T17:30:32.367+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:30:32.368+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:35:33.096+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:35:33.103+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:40:33.405+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:40:33.405+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:45:33.657+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:45:33.663+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:50:33.855+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:50:33.855+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T17:55:34.035+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T17:55:34.036+0000] {manager.py:758} INFO - There are 0 files in /opt/airflow/dags
[2025-08-19T18:03:24.456+0000] {manager.py:487} INFO - Processing files using up to 2 processes at a time 
[2025-08-19T18:03:24.617+0000] {manager.py:488} INFO - Process each file at most once every 30 seconds
[2025-08-19T18:03:24.624+0000] {manager.py:489} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-08-19T18:03:24.624+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T18:03:24.627+0000] {manager.py:758} INFO - There are 1 files in /opt/airflow/dags
[2025-08-19T18:03:25.663+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  ----------
/opt/airflow/dags/dashboard_queries_pipeline.py     43  0.27s             0           0
================================================================================
[2025-08-19T18:03:55.794+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  2.81s           2025-08-19T18:03:28
================================================================================
[2025-08-19T18:04:25.929+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  0.41s           2025-08-19T18:03:58
================================================================================
[2025-08-19T18:04:56.073+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  0.14s           2025-08-19T18:04:29
================================================================================
[2025-08-19T18:05:26.378+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  0.28s           2025-08-19T18:04:59
================================================================================
[2025-08-19T18:05:56.746+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  0.25s           2025-08-19T18:05:29
================================================================================
[2025-08-19T18:06:27.217+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  0.25s           2025-08-19T18:06:00
================================================================================
[2025-08-19T18:06:57.703+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
-----------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/dashboard_queries_pipeline.py                           1           0  0.28s           2025-08-19T18:06:30
================================================================================
[2025-08-19T18:07:27.240+0000] {manager.py:1137} WARNING - Skipping processing of missing file: /opt/airflow/dags/dashboard_queries_pipeline.py
[2025-08-19T18:08:24.814+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T18:08:24.815+0000] {manager.py:758} INFO - There are 1 files in /opt/airflow/dags
[2025-08-19T18:08:28.843+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.16s           2025-08-19T18:08:24
================================================================================
[2025-08-19T18:08:59.125+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:08:55
================================================================================
[2025-08-19T18:09:29.327+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.15s           2025-08-19T18:09:25
================================================================================
[2025-08-19T18:09:59.464+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.09s           2025-08-19T18:09:55
================================================================================
[2025-08-19T18:10:29.667+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.15s           2025-08-19T18:10:25
================================================================================
[2025-08-19T18:10:59.795+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:10:55
================================================================================
[2025-08-19T18:11:29.974+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.09s           2025-08-19T18:11:25
================================================================================
[2025-08-19T18:12:00.136+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:11:56
================================================================================
[2025-08-19T18:12:30.339+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.25s           2025-08-19T18:12:26
================================================================================
[2025-08-19T18:13:00.677+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.10s           2025-08-19T18:12:56
================================================================================
[2025-08-19T18:13:24.884+0000] {manager.py:755} INFO - Searching for files in /opt/airflow/dags
[2025-08-19T18:13:24.894+0000] {manager.py:758} INFO - There are 1 files in /opt/airflow/dags
[2025-08-19T18:13:30.817+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.12s           2025-08-19T18:13:26
================================================================================
[2025-08-19T18:14:00.963+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:13:57
================================================================================
[2025-08-19T18:14:31.150+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:14:27
================================================================================
[2025-08-19T18:15:01.279+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:14:57
================================================================================
[2025-08-19T18:15:31.510+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.09s           2025-08-19T18:15:27
================================================================================
[2025-08-19T18:16:01.649+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.08s           2025-08-19T18:15:57
================================================================================
[2025-08-19T18:16:31.833+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.14s           2025-08-19T18:16:27
================================================================================
[2025-08-19T18:17:02.300+0000] {manager.py:887} INFO - 
================================================================================
DAG File Processing Stats

File Path                          PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pipeline_dag.py                           1           0  0.20s           2025-08-19T18:16:38
================================================================================
