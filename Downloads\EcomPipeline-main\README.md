# E-commerce Data Pipeline

A comprehensive data pipeline that ingests e-commerce data using Apache NiFi, stores it in HDFS, processes it with Apache Spark, loads it into a PostgreSQL data warehouse, and visualizes insights using Grafana. The entire pipeline is orchestrated using Apache Airflow.

## 🏗️ Architecture

```
Data Source → NiFi → HDFS → Spark → PostgreSQL → Grafana
                ↑                      ↑
            Airflow Orchestration ──────┘
```

## 🛠️ Technology Stack

- **Data Ingestion**: Apache NiFi
- **Data Storage**: Hadoop HDFS
- **Data Processing**: Apache Spark
- **Data Warehouse**: PostgreSQL
- **Visualization**: Grafana
- **Orchestration**: Apache Airflow
- **Containerization**: Docker & Docker Compose

## 📊 Pipeline Components

### 1. Data Ingestion (NiFi)

- Ingests CSV data from various sources
- Handles data validation and routing
- Stores raw data in HDFS

### 2. Data Storage (HDFS)

- Distributed storage for raw and processed data
- Fault-tolerant and scalable
- Organized directory structure for different data stages

### 3. Data Processing (Spark)

- Processes raw e-commerce data
- Generates marketing insights and KPIs
- Calculates metrics like:
  - Revenue by payment method
  - Newsletter subscription impact
  - Browser performance analytics

### 4. Data Warehouse (PostgreSQL)

- Stores processed analytical data
- Optimized for reporting and visualization
- Contains tables:
  - `revenue_by_paymethod`
  - `newsletter_stats`
  - `browser_stats`

### 5. Visualization (Grafana)

- Interactive dashboards for business insights
- Real-time monitoring of KPIs
- Customizable charts and graphs

### 6. Orchestration (Airflow)

- Schedules and monitors the entire pipeline
- Handles dependencies and error recovery
- Provides web UI for pipeline management

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 8GB RAM available
- Ports 3000, 5433, 8080, 8081, 8443, 9000, 9870 available

### Setup Instructions

1. **Clone and navigate to the project**:

   ```bash
   cd EcomPipeline-main
   ```

2. **Make setup script executable**:

   ```bash
   chmod +x setup_pipeline.sh
   ```

3. **Run the setup script**:

   ```bash
   ./setup_pipeline.sh
   ```

4. **Access the services**:
   - Airflow: http://localhost:8081 (admin/admin)
   - NiFi: https://localhost:8443 (admin/admin123456789)
   - Grafana: http://localhost:3000 (admin/admin)
   - Spark Master: http://localhost:8080
   - HDFS NameNode: http://localhost:9870

## 📋 Pipeline Execution

### Automatic Execution

The pipeline runs daily at midnight by default. You can modify the schedule in the DAG file.

### Manual Execution

1. Access Airflow at http://localhost:8081
2. Enable the `ecom_data_pipeline` DAG
3. Click "Trigger DAG" to run manually

### Command Line Execution

```bash
docker-compose exec airflow-webserver airflow dags trigger ecom_data_pipeline
```

## 📈 Monitoring and Visualization

### Airflow Monitoring

- View pipeline status and logs in Airflow UI
- Monitor task execution times and success rates
- Set up alerts for pipeline failures

### Grafana Dashboards

- Revenue analysis by payment methods
- Customer segmentation by newsletter subscription
- Browser performance metrics
- Time-based trends and patterns

## 🔧 Configuration

### Airflow DAG Configuration

The main pipeline DAG is located at `dags/pipeline_dag.py` and includes:

- Health checks for all services
- Data ingestion from NiFi
- HDFS data validation
- Spark job execution
- Data quality checks
- Grafana dashboard refresh

### Spark Job Configuration

The Spark processing job (`marketing_job.py`) performs:

- Data cleaning and transformation
- Marketing KPI calculations
- PostgreSQL data loading

### NiFi Configuration

Configure NiFi processors for:

- Data source connections
- Data validation rules
- HDFS output paths

## 🗂️ Data Schema

### Input Data (CSV)

```
Customer_id, Age, Gender, Revenue_Total, N_Purchases,
Purchase_DATE, Purchase_VALUE, Pay_Method, Time_Spent,
Browser, Newsletter, Voucher
```

### Output Tables

#### revenue_by_paymethod

- `pay_method`: Payment method ID
- `total_revenue`: Total revenue for each payment method

#### newsletter_stats

- `newsletter`: Newsletter subscription status
- `num_customers`: Number of customers
- `avg_revenue`: Average revenue per customer

#### browser_stats

- `browser`: Browser type ID
- `avg_time_spent`: Average time spent on site
- `avg_purchase_value`: Average purchase value

## 🛠️ Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker resources and port availability
2. **HDFS connection issues**: Ensure namenode is healthy before running pipeline
3. **Spark job failures**: Check PostgreSQL connection and table permissions
4. **Grafana no data**: Verify PostgreSQL datasource configuration

### Logs and Debugging

- Airflow logs: `logs/` directory
- Container logs: `docker-compose logs <service-name>`
- HDFS logs: Access through NameNode UI at http://localhost:9870

## 🔄 Pipeline Customization

### Adding New Data Sources

1. Configure new NiFi processors
2. Update HDFS directory structure
3. Modify Spark job for new data schema
4. Create corresponding PostgreSQL tables
5. Update Grafana dashboards

### Modifying Processing Logic

1. Edit `marketing_job.py` for new transformations
2. Update PostgreSQL table schemas
3. Modify Grafana queries and visualizations

### Changing Schedule

Modify the `schedule_interval` parameter in the DAG:

```python
schedule_interval='@hourly'  # Run every hour
schedule_interval='0 */6 * * *'  # Run every 6 hours
```

## 📝 License

This project is open source and available under the MIT License.
