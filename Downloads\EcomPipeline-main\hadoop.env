CORE_CONF_fs_defaultFS=hdfs://namenode:9000
CORE_CONF_hadoop_http_staticuser_user=root
CORE_CONF_hadoop_proxyuser_hue_hosts=*
CORE_CONF_hadoop_proxyuser_hue_groups=*
CORE_CONF_io_compression_codecs=org.apache.hadoop.io.compress.SnappyCodec

HDFS_CONF_dfs_webhdfs_enabled=true
HDFS_CONF_dfs_permissions_enabled=false
HDFS_CONF_dfs_namenode_name_dir=file:///hadoop/dfs/name
HDFS_CONF_dfs_datanode_data_dir=file:///hadoop/dfs/data
HDFS_CONF_dfs_replication=1

MAPRED_CONF_mapreduce_framework_name=yarn
MAPRED_CONF_mapred_child_java_opts=-Xmx512m
MAPRED_CONF_mapreduce_map_memory_mb=512
MAPRED_CONF_mapreduce_reduce_memory_mb=1024
MAPRED_CONF_mapreduce_map_java_opts=-Xmx384m
MAPRED_CONF_mapreduce_reduce_java_opts=-Xmx768m

HIVE_SITE_CONF_javax_jdo_option_ConnectionURL=*****************************************************
HIVE_SITE_CONF_javax_jdo_option_ConnectionDriverName=org.postgresql.Driver
HIVE_SITE_CONF_javax_jdo_option_ConnectionUserName=hive
HIVE_SITE_CONF_javax_jdo_option_ConnectionPassword=hive
HIVE_SITE_CONF_datanucleus_autoCreateSchema=false
HIVE_SITE_CONF_hive_metastore_uris=thrift://hive-metastore:9083