from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator

# Default arguments for DAG
default_args = {
    'owner': 'oussama',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Define DAG
with DAG(
    'ecom_pipeline',
    default_args=default_args,
    description='ETL pipeline: CSV -> HDFS -> Spark -> PostgreSQL Warehouse',
    schedule_interval='@daily',  # Runs once per day (adjust as needed)
    start_date=datetime(2025, 8, 19),
    catchup=False,
    tags=['ecommerce', 'pipeline'],
) as dag:

    # Step 1: Ingest CSV to HDFS
    ingest_to_hdfs = BashOperator(
        task_id='ingest_to_hdfs',
        bash_command="hdfs dfs -put -f /app/data/ecommerce.csv /data/raw/ecommerce.csv"
    )

    # Step 2: Process with Spark
    spark_process = BashOperator(
        task_id='spark_process',
        bash_command="spark-submit --master local /app/spark_jobs/transform.py"
    )

    # Step 3: Load transformed data into PostgreSQL warehouse
    load_to_postgres = BashOperator(
        task_id='load_to_postgres',
        bash_command=(
            "psql -h warehouse-db -p 5432 -U warehouse_user -d warehouse "
            "-c \"\\copy sales from '/app/output/cleaned_data.csv' CSV HEADER\""
        ),
        env={
            "PGPASSWORD": "warehouse_pass"
        }
    )

    # Example: Run SQL check inside Postgres
    quality_check = PostgresOperator(
        task_id='quality_check',
        postgres_conn_id='warehouse_postgres',  # Needs connection in Airflow UI
        sql="SELECT COUNT(*) FROM sales;"
    )

    # DAG dependencies
    ingest_to_hdfs >> spark_process >> load_to_postgres >> quality_check
