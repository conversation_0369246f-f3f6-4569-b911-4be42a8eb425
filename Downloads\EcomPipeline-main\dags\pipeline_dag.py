from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.operators.python import Python<PERSON>perator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.providers.http.sensors.http import HttpSensor
from airflow.sensors.filesystem import FileSensor
import requests
import json
import logging

# Default arguments for DAG
default_args = {
    'owner': 'data-engineer',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'execution_timeout': timedelta(hours=2),
}

def check_nifi_status():
    """Check if NiFi is running and healthy"""
    try:
        response = requests.get('https://nifi:8443/nifi-api/system-diagnostics',
                              verify=False, timeout=30)
        if response.status_code == 200:
            logging.info("NiFi is healthy and running")
            return True
        else:
            logging.error(f"NiFi health check failed with status: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"Failed to connect to NiFi: {str(e)}")
        return False

def trigger_nifi_processor():
    """Trigger NiFi processor to start data ingestion"""
    try:
        # This would typically involve NiFi REST API calls to start processors
        # For now, we'll simulate the trigger
        logging.info("Triggering NiFi data ingestion processor")

        # Example NiFi API call (adjust processor ID as needed)
        # headers = {'Content-Type': 'application/json'}
        # processor_id = "your-processor-id"
        # url = f"https://nifi:8443/nifi-api/processors/{processor_id}/run-status"
        # data = {"revision": {"version": 0}, "state": "RUNNING"}
        # response = requests.put(url, headers=headers, json=data, verify=False)

        return "NiFi processor triggered successfully"
    except Exception as e:
        logging.error(f"Failed to trigger NiFi processor: {str(e)}")
        raise

def check_hdfs_data():
    """Check if data exists in HDFS"""
    try:
        # Check if the dataset exists in HDFS
        result = BashOperator(
            task_id='temp_hdfs_check',
            bash_command="hdfs dfs -test -e /user/marketing/datasets/dataset.csv && echo 'File exists' || echo 'File not found'"
        )
        return "HDFS data check completed"
    except Exception as e:
        logging.error(f"HDFS data check failed: {str(e)}")
        raise

def validate_spark_output():
    """Validate Spark job output in PostgreSQL"""
    try:
        postgres_hook = PostgresHook(postgres_conn_id='warehouse_postgres')

        # Check if tables were created and have data
        tables_to_check = ['revenue_by_paymethod', 'newsletter_stats', 'browser_stats']

        for table in tables_to_check:
            count_query = f"SELECT COUNT(*) FROM {table};"
            result = postgres_hook.get_first(count_query)
            row_count = result[0] if result else 0

            if row_count > 0:
                logging.info(f"Table {table} has {row_count} rows")
            else:
                logging.warning(f"Table {table} is empty or doesn't exist")

        return "Spark output validation completed"
    except Exception as e:
        logging.error(f"Spark output validation failed: {str(e)}")
        raise

def refresh_grafana_dashboards():
    """Refresh Grafana dashboards to show latest data"""
    try:
        # Grafana API call to refresh dashboards
        grafana_url = "http://grafana:3000"
        auth = ('admin', 'admin')

        # Get list of dashboards
        dashboards_response = requests.get(
            f"{grafana_url}/api/search?type=dash-db",
            auth=auth,
            timeout=30
        )

        if dashboards_response.status_code == 200:
            dashboards = dashboards_response.json()
            logging.info(f"Found {len(dashboards)} dashboards to refresh")

            # Trigger refresh for each dashboard (this is conceptual)
            for dashboard in dashboards:
                logging.info(f"Dashboard {dashboard.get('title', 'Unknown')} ready for refresh")

        return "Grafana dashboards refreshed successfully"
    except Exception as e:
        logging.error(f"Failed to refresh Grafana dashboards: {str(e)}")
        raise

# Define DAG
with DAG(
    'ecom_data_pipeline',
    default_args=default_args,
    description='Complete E-commerce Data Pipeline: NiFi -> HDFS -> Spark -> PostgreSQL -> Grafana',
    schedule_interval='@daily',  # Runs once per day at midnight
    start_date=datetime(2025, 8, 19),
    catchup=False,
    tags=['ecommerce', 'pipeline', 'etl', 'nifi', 'spark', 'grafana'],
    max_active_runs=1,  # Prevent overlapping runs
) as dag:

    # Step 1: Health checks for all services
    nifi_health_check = PythonOperator(
        task_id='nifi_health_check',
        python_callable=check_nifi_status,
        doc_md="""
        ## NiFi Health Check
        Verifies that NiFi service is running and accessible before starting data ingestion.
        """
    )

    hdfs_health_check = BashOperator(
        task_id='hdfs_health_check',
        bash_command="hdfs dfsadmin -report | grep 'Live datanodes' || exit 1",
        doc_md="""
        ## HDFS Health Check
        Verifies that HDFS cluster is healthy and datanodes are available.
        """
    )

    postgres_health_check = PostgresOperator(
        task_id='postgres_health_check',
        postgres_conn_id='warehouse_postgres',
        sql="SELECT 1;",
        doc_md="""
        ## PostgreSQL Health Check
        Verifies that PostgreSQL warehouse is accessible and responsive.
        """
    )

    # Step 2: Prepare HDFS directories
    prepare_hdfs_directories = BashOperator(
        task_id='prepare_hdfs_directories',
        bash_command="""
        hdfs dfs -mkdir -p /user/marketing/datasets
        hdfs dfs -mkdir -p /user/marketing/processed
        hdfs dfs -mkdir -p /user/marketing/archive
        echo "HDFS directories prepared successfully"
        """,
        doc_md="""
        ## Prepare HDFS Directories
        Creates necessary directory structure in HDFS for data storage.
        """
    )

    # Step 3: Trigger NiFi data ingestion
    trigger_nifi_ingestion = PythonOperator(
        task_id='trigger_nifi_ingestion',
        python_callable=trigger_nifi_processor,
        doc_md="""
        ## Trigger NiFi Data Ingestion
        Starts NiFi processor to ingest data from source to HDFS.
        """
    )

    # Step 4: Wait for data to be available in HDFS
    wait_for_hdfs_data = FileSensor(
        task_id='wait_for_hdfs_data',
        filepath='/user/marketing/datasets/dataset.csv',
        fs_conn_id='hdfs_default',
        timeout=1800,  # 30 minutes timeout
        poke_interval=60,  # Check every minute
        doc_md="""
        ## Wait for HDFS Data
        Waits for the dataset to be available in HDFS before proceeding.
        """
    )

    # Alternative: Manual data upload to HDFS (if NiFi is not configured)
    manual_hdfs_upload = BashOperator(
        task_id='manual_hdfs_upload',
        bash_command="""
        # Copy dataset from local to HDFS
        hdfs dfs -put -f /opt/airflow/dags/../dataset.csv /user/marketing/datasets/dataset.csv
        hdfs dfs -ls /user/marketing/datasets/
        echo "Dataset uploaded to HDFS successfully"
        """,
        doc_md="""
        ## Manual HDFS Upload
        Fallback option to manually upload dataset to HDFS if NiFi ingestion fails.
        """
    )

    # Step 5: Validate HDFS data
    validate_hdfs_data = BashOperator(
        task_id='validate_hdfs_data',
        bash_command="""
        # Check if file exists and get basic stats
        hdfs dfs -test -e /user/marketing/datasets/dataset.csv
        if [ $? -eq 0 ]; then
            echo "Dataset exists in HDFS"
            hdfs dfs -du -h /user/marketing/datasets/dataset.csv
            hdfs dfs -head /user/marketing/datasets/dataset.csv | head -5
        else
            echo "Dataset not found in HDFS"
            exit 1
        fi
        """,
        doc_md="""
        ## Validate HDFS Data
        Validates that the dataset is properly stored in HDFS and shows basic information.
        """
    )

    # Step 6: Run Spark processing job
    spark_processing = BashOperator(
        task_id='spark_processing',
        bash_command="""
        # Submit Spark job for data processing
        spark-submit \
            --master spark://spark-master:7077 \
            --deploy-mode client \
            --driver-memory 1g \
            --executor-memory 2g \
            --executor-cores 2 \
            --jars /opt/airflow/dags/../postgresql-42.6.0.jar \
            --conf spark.sql.adaptive.enabled=true \
            --conf spark.sql.adaptive.coalescePartitions.enabled=true \
            /opt/airflow/dags/../marketing_job.py
        """,
        doc_md="""
        ## Spark Data Processing
        Runs the Spark job to process raw data and generate marketing insights.
        """
    )

    # Step 7: Validate Spark output
    validate_spark_output = PythonOperator(
        task_id='validate_spark_output',
        python_callable=validate_spark_output,
        doc_md="""
        ## Validate Spark Output
        Validates that Spark job successfully created tables in PostgreSQL warehouse.
        """
    )

    # Step 8: Data Quality Checks
    data_quality_checks = PostgresOperator(
        task_id='data_quality_checks',
        postgres_conn_id='warehouse_postgres',
        sql="""
        -- Check for data completeness and quality
        DO $$
        DECLARE
            revenue_count INTEGER;
            newsletter_count INTEGER;
            browser_count INTEGER;
        BEGIN
            -- Check revenue_by_paymethod table
            SELECT COUNT(*) INTO revenue_count FROM revenue_by_paymethod;
            IF revenue_count = 0 THEN
                RAISE EXCEPTION 'revenue_by_paymethod table is empty';
            END IF;

            -- Check newsletter_stats table
            SELECT COUNT(*) INTO newsletter_count FROM newsletter_stats;
            IF newsletter_count = 0 THEN
                RAISE EXCEPTION 'newsletter_stats table is empty';
            END IF;

            -- Check browser_stats table
            SELECT COUNT(*) INTO browser_count FROM browser_stats;
            IF browser_count = 0 THEN
                RAISE EXCEPTION 'browser_stats table is empty';
            END IF;

            -- Log success
            RAISE NOTICE 'Data quality checks passed: revenue_count=%, newsletter_count=%, browser_count=%',
                         revenue_count, newsletter_count, browser_count;
        END $$;
        """,
        doc_md="""
        ## Data Quality Checks
        Performs comprehensive data quality validation on processed tables.
        """
    )

    # Step 9: Create/Update Grafana data source (if needed)
    setup_grafana_datasource = BashOperator(
        task_id='setup_grafana_datasource',
        bash_command="""
        # Create PostgreSQL data source in Grafana via API
        curl -X POST \
          -H "Content-Type: application/json" \
          -u admin:admin \
          http://grafana:3000/api/datasources \
          -d '{
            "name": "PostgreSQL Warehouse",
            "type": "postgres",
            "url": "warehouse-db:5432",
            "database": "warehouse",
            "user": "warehouse_user",
            "password": "warehouse_pass",
            "access": "proxy",
            "isDefault": true
          }' || echo "Data source might already exist"
        """,
        doc_md="""
        ## Setup Grafana Data Source
        Configures PostgreSQL warehouse as a data source in Grafana.
        """
    )

    # Step 10: Refresh Grafana dashboards
    refresh_grafana = PythonOperator(
        task_id='refresh_grafana_dashboards',
        python_callable=refresh_grafana_dashboards,
        doc_md="""
        ## Refresh Grafana Dashboards
        Refreshes Grafana dashboards to display the latest processed data.
        """
    )

    # Step 11: Archive processed data
    archive_processed_data = BashOperator(
        task_id='archive_processed_data',
        bash_command="""
        # Archive the processed dataset with timestamp
        TIMESTAMP=$(date +%Y%m%d_%H%M%S)
        hdfs dfs -cp /user/marketing/datasets/dataset.csv /user/marketing/archive/dataset_$TIMESTAMP.csv
        echo "Data archived with timestamp: $TIMESTAMP"
        """,
        doc_md="""
        ## Archive Processed Data
        Archives the processed dataset for historical tracking and compliance.
        """
    )

    # Step 12: Cleanup temporary files
    cleanup_temp_files = BashOperator(
        task_id='cleanup_temp_files',
        bash_command="""
        # Clean up any temporary files created during processing
        echo "Cleaning up temporary files..."
        # Add specific cleanup commands as needed
        echo "Cleanup completed successfully"
        """,
        doc_md="""
        ## Cleanup Temporary Files
        Removes temporary files and performs housekeeping tasks.
        """
    )

    # Step 13: Send completion notification (optional)
    pipeline_completion_notification = BashOperator(
        task_id='pipeline_completion_notification',
        bash_command="""
        echo "E-commerce Data Pipeline completed successfully at $(date)"
        echo "Data processed and available in PostgreSQL warehouse"
        echo "Grafana dashboards updated with latest insights"
        # Add email notification or Slack webhook here if needed
        """,
        doc_md="""
        ## Pipeline Completion Notification
        Sends notification about successful pipeline completion.
        """
    )

    # ============================================
    # TASK DEPENDENCIES
    # ============================================

    # Health checks run in parallel first
    [nifi_health_check, hdfs_health_check, postgres_health_check] >> prepare_hdfs_directories

    # Data ingestion flow
    prepare_hdfs_directories >> trigger_nifi_ingestion

    # Two paths: wait for NiFi or manual upload (choose one based on setup)
    trigger_nifi_ingestion >> [wait_for_hdfs_data, manual_hdfs_upload]

    # Both paths converge to validation
    [wait_for_hdfs_data, manual_hdfs_upload] >> validate_hdfs_data

    # Processing pipeline
    validate_hdfs_data >> spark_processing >> validate_spark_output

    # Data quality and visualization
    validate_spark_output >> data_quality_checks
    data_quality_checks >> setup_grafana_datasource >> refresh_grafana

    # Cleanup and notification
    refresh_grafana >> [archive_processed_data, cleanup_temp_files]
    [archive_processed_data, cleanup_temp_files] >> pipeline_completion_notification
