from pyspark.sql import SparkSession
from pyspark.sql.functions import col, when, sum as spark_sum, avg, count, to_date
from pyspark.sql.types import StructType, StructField, IntegerType, DoubleType, StringType

# 1️⃣ Initialize Spark
spark = SparkSession.builder \
    .appName("MarketingInsightsPipeline") \
    .config("spark.jars.packages", "org.postgresql:postgresql:42.6.0") \
    .getOrCreate()

# 2️⃣ Define schema manually
schema = StructType([
    StructField("Customer_id", StringType(), True),
    StructField("Age", IntegerType(), True),
    StructField("Gender", IntegerType(), True),
    StructField("Revenue_Total", DoubleType(), True),
    StructField("N_Purchases", IntegerType(), True),
    StructField("Purchase_DATE", StringType(), True),
    StructField("Purchase_VALUE", DoubleType(), True),
    StructField("Pay_Method", IntegerType(), True),
    StructField("Time_Spent", DoubleType(), True),
    StructField("Browser", IntegerType(), True),
    StructField("Newsletter", IntegerType(), True),
    StructField("Voucher", IntegerType(), True)
])

# 3️⃣ Read dataset from HDFS (hidden file)
df = spark.read.option("ignoreLeadingWhiteSpace", True) \
               .option("ignoreTrailingWhiteSpace", True) \
               .csv("hdfs://namenode:9000/user/marketing/datasets/dataset.csv", header=True, schema=schema)
               


# 4️⃣ Data type & cleaning transformations
df_clean = df \
    .withColumn("Purchase_DATE", to_date(col("Purchase_DATE"), "dd.MM.yy")) \
    .withColumn("Gender", col("Gender").cast("integer")) \
    .withColumn("Pay_Method", col("Pay_Method").cast("integer")) \
    .withColumn("Browser", col("Browser").cast("integer")) \
    .withColumn("Newsletter", col("Newsletter").cast("integer")) \
    .withColumn("Voucher", col("Voucher").cast("integer")) \
    .withColumn("Age", col("Age").cast("integer")) \
    .withColumn("Revenue_Total", col("Revenue_Total").cast("double")) \
    .withColumn("N_Purchases", col("N_Purchases").cast("integer")) \
    .withColumn("Purchase_VALUE", col("Purchase_VALUE").cast("double")) \
    .withColumn("Time_Spent", col("Time_Spent").cast("double"))

# 5️⃣ Example transformations for marketing KPIs
avg_revenue = df_clean.groupBy().agg(avg("Revenue_Total").alias("Avg_Revenue")).collect()[0]["Avg_Revenue"]

revenue_by_paymethod = df_clean.groupBy("Pay_Method").agg(spark_sum("Revenue_Total").alias("Total_Revenue"))

newsletter_stats = df_clean.groupBy("Newsletter").agg(
    count("Customer_id").alias("Num_Customers"),
    avg("Revenue_Total").alias("Avg_Revenue")
)

browser_stats = df_clean.groupBy("Browser").agg(
    avg("Time_Spent").alias("Avg_Time_Spent"),
    avg("Purchase_VALUE").alias("Avg_Purchase_Value")
)

# 6️⃣ Write results to PostgreSQL
jdbc_url = "*********************************************"  # use container name, not localhost
properties = {
    "user": "warehouse_user",
    "password": "warehouse_pass",
    "driver": "org.postgresql.Driver"
}


revenue_by_paymethod.write.jdbc(url=jdbc_url, table="revenue_by_paymethod", mode="overwrite", properties=properties)
newsletter_stats.write.jdbc(url=jdbc_url, table="newsletter_stats", mode="overwrite", properties=properties)
browser_stats.write.jdbc(url=jdbc_url, table="browser_stats", mode="overwrite", properties=properties)

print(f"Average revenue per customer: {avg_revenue}")

spark.stop()
