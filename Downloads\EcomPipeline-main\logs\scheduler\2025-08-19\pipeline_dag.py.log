[2025-08-19T18:08:24.837+0000] {processor.py:157} INFO - Started process (PID=115) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:08:24.838+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:08:24.839+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:08:24.838+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:08:24.850+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:08:24.946+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:08:24.946+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:08:24.963+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:08:24.962+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-18T00:00:00+00:00, run_after=2025-08-19T00:00:00+00:00
[2025-08-19T18:08:24.978+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.144 seconds
[2025-08-19T18:08:55.115+0000] {processor.py:157} INFO - Started process (PID=124) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:08:55.116+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:08:55.117+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:08:55.117+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:08:55.128+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:08:55.151+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:08:55.151+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:08:55.169+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:08:55.169+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-18T00:00:00+00:00, run_after=2025-08-19T00:00:00+00:00
[2025-08-19T18:08:55.183+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.070 seconds
[2025-08-19T18:09:25.286+0000] {processor.py:157} INFO - Started process (PID=133) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:09:25.288+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:09:25.292+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:09:25.291+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:09:25.311+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:09:25.344+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:09:25.344+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:09:25.373+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:09:25.372+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-18T00:00:00+00:00, run_after=2025-08-19T00:00:00+00:00
[2025-08-19T18:09:25.396+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.116 seconds
[2025-08-19T18:09:55.454+0000] {processor.py:157} INFO - Started process (PID=142) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:09:55.455+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:09:55.456+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:09:55.455+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:09:55.468+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:09:55.493+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:09:55.492+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:09:55.513+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:09:55.513+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:09:55.525+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.073 seconds
[2025-08-19T18:10:25.630+0000] {processor.py:157} INFO - Started process (PID=151) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:10:25.631+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:10:25.632+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:10:25.631+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:10:25.643+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:10:25.669+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:10:25.669+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:10:25.724+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:10:25.724+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:10:25.757+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.129 seconds
[2025-08-19T18:10:55.787+0000] {processor.py:157} INFO - Started process (PID=160) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:10:55.787+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:10:55.788+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:10:55.788+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:10:55.798+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:10:55.821+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:10:55.821+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:10:55.838+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:10:55.838+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:10:55.851+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.067 seconds
[2025-08-19T18:11:25.912+0000] {processor.py:157} INFO - Started process (PID=169) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:11:25.913+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:11:25.913+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:11:25.913+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:11:25.924+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:11:25.947+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:11:25.946+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:11:25.967+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:11:25.966+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:11:25.983+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.073 seconds
[2025-08-19T18:11:56.120+0000] {processor.py:157} INFO - Started process (PID=178) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:11:56.121+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:11:56.121+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:11:56.121+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:11:56.131+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:11:56.153+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:11:56.153+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:11:56.171+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:11:56.171+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:11:56.184+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.068 seconds
[2025-08-19T18:12:26.277+0000] {processor.py:157} INFO - Started process (PID=187) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:12:26.278+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:12:26.279+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:12:26.279+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:12:26.298+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:12:26.353+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:12:26.352+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:12:26.415+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:12:26.415+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:12:26.481+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.210 seconds
[2025-08-19T18:12:56.666+0000] {processor.py:157} INFO - Started process (PID=196) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:12:56.667+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:12:56.667+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:12:56.667+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:12:56.682+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:12:56.715+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:12:56.714+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:12:56.737+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:12:56.737+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:12:56.751+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.088 seconds
[2025-08-19T18:13:26.807+0000] {processor.py:157} INFO - Started process (PID=205) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:13:26.809+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:13:26.810+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:13:26.809+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:13:26.825+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:13:26.854+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:13:26.854+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:13:26.880+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:13:26.880+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:13:26.898+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.099 seconds
[2025-08-19T18:13:56.942+0000] {processor.py:157} INFO - Started process (PID=206) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:13:56.943+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:13:56.943+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:13:56.943+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:13:56.955+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:13:56.978+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:13:56.978+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:13:56.996+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:13:56.996+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:13:57.008+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.069 seconds
[2025-08-19T18:14:27.079+0000] {processor.py:157} INFO - Started process (PID=215) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:14:27.080+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:14:27.081+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:14:27.080+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:14:27.091+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:14:27.114+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:14:27.114+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:14:27.132+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:14:27.132+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:14:27.145+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.069 seconds
[2025-08-19T18:14:57.266+0000] {processor.py:157} INFO - Started process (PID=224) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:14:57.266+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:14:57.267+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:14:57.267+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:14:57.277+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:14:57.299+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:14:57.299+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:14:57.317+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:14:57.317+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:14:57.329+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.066 seconds
[2025-08-19T18:15:27.408+0000] {processor.py:157} INFO - Started process (PID=234) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:15:27.409+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:15:27.410+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:15:27.409+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:15:27.420+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:15:27.442+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:15:27.442+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:15:27.464+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:15:27.464+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:15:27.479+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.074 seconds
[2025-08-19T18:15:57.635+0000] {processor.py:157} INFO - Started process (PID=243) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:15:57.636+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:15:57.637+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:15:57.636+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:15:57.647+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:15:57.668+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:15:57.668+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:15:57.685+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:15:57.685+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:15:57.698+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.066 seconds
[2025-08-19T18:16:27.779+0000] {processor.py:157} INFO - Started process (PID=252) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:27.780+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:16:27.781+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:27.781+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:27.806+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:27.844+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:27.843+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:16:27.872+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:27.872+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:16:27.889+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.116 seconds
[2025-08-19T18:16:33.849+0000] {processor.py:157} INFO - Started process (PID=253) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:33.850+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:16:33.850+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:33.850+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:33.932+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:33.854+0000] {dagbag.py:346} ERROR - Failed to import: /opt/airflow/dags/pipeline_dag.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/models/dagbag.py", line 342, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/opt/airflow/dags/pipeline_dag.py", line 22, in <module>
    start_date=datetime(2025, 8, ),
TypeError: function missing required argument 'day' (pos 3)
[2025-08-19T18:16:33.933+0000] {processor.py:841} WARNING - No viable dags retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:33.975+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.128 seconds
[2025-08-19T18:16:36.967+0000] {processor.py:157} INFO - Started process (PID=254) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:36.968+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:16:36.971+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:36.971+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:37.008+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:37.057+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:37.057+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:16:37.114+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:37.114+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:16:37.152+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.194 seconds
[2025-08-19T18:16:38.012+0000] {processor.py:157} INFO - Started process (PID=255) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:38.013+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:16:38.014+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:38.014+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:38.025+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:16:38.162+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:38.162+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:16:38.179+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:16:38.179+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:16:38.193+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.184 seconds
[2025-08-19T18:17:08.342+0000] {processor.py:157} INFO - Started process (PID=264) to work on /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:17:08.343+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/pipeline_dag.py for tasks to queue
[2025-08-19T18:17:08.344+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:17:08.344+0000] {dagbag.py:536} INFO - Filling up the DagBag from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:17:08.357+0000] {processor.py:839} INFO - DAG(s) dict_keys(['ecom_pipeline']) retrieved from /opt/airflow/dags/pipeline_dag.py
[2025-08-19T18:17:08.378+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:17:08.378+0000] {dag.py:2941} INFO - Sync 1 DAGs
[2025-08-19T18:17:08.396+0000] {logging_mixin.py:154} INFO - [2025-08-19T18:17:08.396+0000] {dag.py:3722} INFO - Setting next_dagrun for ecom_pipeline to 2025-08-19T00:00:00+00:00, run_after=2025-08-20T00:00:00+00:00
[2025-08-19T18:17:08.409+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/pipeline_dag.py took 0.074 seconds
