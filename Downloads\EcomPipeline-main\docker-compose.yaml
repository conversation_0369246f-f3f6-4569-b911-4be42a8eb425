version: '3.8'

services:
  # ============================================
  # HADOOP HDFS CLUSTER
  # ============================================
  namenode:
    image: bde2020/hadoop-namenode:2.0.0-hadoop3.2.1-java8
    container_name: namenode
    restart: unless-stopped
    ports:
      - "9870:9870"
      - "9000:9000"
    volumes:
      - hadoop_namenode:/hadoop/dfs/name
    environment:
      - CLUSTER_NAME=data-cluster
      - CORE_CONF_fs_defaultFS=hdfs://namenode:9000
    env_file:
      - ./hadoop.env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9870"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - data-pipeline

  datanode:
    image: bde2020/hadoop-datanode:2.0.0-hadoop3.2.1-java8
    container_name: datanode
    restart: unless-stopped
    ports:
      - "9864:9864"
    volumes:
      - hadoop_datanode:/hadoop/dfs/data
    environment:
      - SERVICE_PRECONDITION=namenode:9870
      - CORE_CONF_fs_defaultFS=hdfs://namenode:9000
    env_file:
      - ./hadoop.env
    depends_on:
      namenode:
        condition: service_healthy
    networks:
      - data-pipeline

  # ============================================
  # APACHE SPARK CLUSTER
  # ============================================
  spark-master:
    image: bitnami/spark:3.4
    container_name: spark-master
    command: bin/spark-class org.apache.spark.deploy.master.Master
    ports:
      - "8080:8080"
      - "7077:7077"
    environment:
      - SPARK_MODE=master
      - SPARK_MASTER_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - data-pipeline

  spark-worker:
    image: bitnami/spark:3.4
    container_name: spark-worker
    command: bin/spark-class org.apache.spark.deploy.worker.Worker spark://spark-master:7077
    depends_on:
      - spark-master
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2G
      - SPARK_WORKER_CORES=2
    restart: unless-stopped
    networks:
      - data-pipeline

  # ============================================
  # HIVE DATA WAREHOUSE
  # ============================================
  hive-metastore-db:
    image: bde2020/hive-metastore-postgresql:2.3.0
    container_name: hive-metastore-db
    volumes:
      - hive_postgresql:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  hive-metastore:
    image: bde2020/hive:2.3.2-postgresql-metastore
    container_name: hive-metastore
    command: /opt/hive/bin/hive --service metastore
    env_file:
      - ./hadoop.env
    environment:
      - SERVICE_PRECONDITION=namenode:9870 datanode:9864 hive-metastore-db:5432
    ports:
      - "9083:9083"
    depends_on:
      hive-metastore-db:
        condition: service_healthy
      namenode:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - data-pipeline

  hive-server:
    image: bde2020/hive:2.3.2-postgresql-metastore
    container_name: hive-server
    env_file:
      - ./hadoop.env
    environment:
      - HIVE_CORE_CONF_javax_jdo_option_ConnectionURL=*********************************************
      - SERVICE_PRECONDITION=hive-metastore:9083
    ports:
      - "10000:10000"
    depends_on:
      - hive-metastore
    restart: unless-stopped
    networks:
      - data-pipeline

  # ============================================
  # APACHE NIFI (DATA INGESTION)
  # ============================================
  nifi:
    image: apache/nifi:1.23.2
    container_name: nifi
    restart: unless-stopped
    ports:
      - "8443:8443"
    environment:
      - NIFI_WEB_HTTPS_PORT=8443
      - NIFI_WEB_HTTPS_HOST=0.0.0.0
      - NIFI_CLUSTER_IS_NODE=false
      - NIFI_SENSITIVE_PROPS_KEY=12345678901234567890A
      - SINGLE_USER_CREDENTIALS_USERNAME=admin
      - SINGLE_USER_CREDENTIALS_PASSWORD=admin123456789
      - NIFI_SECURITY_USER_AUTHORIZER=single-user-authorizer
      - NIFI_SECURITY_USER_LOGIN_IDENTITY_PROVIDER=single-user-provider
    volumes:
      - ./data:/data
      - ./config/hadoop/core-site.xml:/opt/hadoop/etc/hadoop/core-site.xml:ro
      - ./config/hadoop/hdfs-site.xml:/opt/hadoop/etc/hadoop/hdfs-site.xml:ro
      - nifi_database_repository:/opt/nifi/nifi-current/database_repository
      - nifi_flowfile_repository:/opt/nifi/nifi-current/flowfile_repository
      - nifi_content_repository:/opt/nifi/nifi-current/content_repository
      - nifi_provenance_repository:/opt/nifi/nifi-current/provenance_repository
      - nifi_state:/opt/nifi/nifi-current/state
      - nifi_logs:/opt/nifi/nifi-current/logs
      - nifi_conf:/opt/nifi/nifi-current/conf
    healthcheck:
      test: ["CMD", "curl", "-k", "-f", "https://localhost:8443/nifi/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - data-pipeline

  # ============================================
  # DATA WAREHOUSE (POSTGRES)
  # ============================================
  warehouse-db:
    image: postgres:15
    container_name: warehouse-db
    environment:
      POSTGRES_USER: warehouse_user
      POSTGRES_PASSWORD: warehouse_pass
      POSTGRES_DB: warehouse
    ports:
      - "5433:5432"
    volumes:
      - warehouse_data:/var/lib/postgresql/data
      - ./scripts/init-warehouse.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U warehouse_user -d warehouse"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ============================================
  # WORKFLOW ORCHESTRATION (AIRFLOW)
  # ============================================
  airflow-db:
    image: postgres:15
    container_name: airflow-db
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - airflow_db:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U airflow -d airflow"]
      interval: 10s
      timeout: 5s
      retries: 5

  airflow-redis:
    image: redis:7-alpine
    container_name: airflow-redis
    restart: unless-stopped
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  airflow-webserver:
    image: apache/airflow:2.7.3
    container_name: airflow-webserver
    command: webserver
    depends_on:
      airflow-db:
        condition: service_healthy
      airflow-redis:
        condition: service_healthy
    environment:
      AIRFLOW__CORE__EXECUTOR: CeleryExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@airflow-db/airflow
      AIRFLOW__CELERY__RESULT_BACKEND: db+***********************************************
      AIRFLOW__CELERY__BROKER_URL: redis://:@airflow-redis:6379/0
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
      AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
      AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session'
      AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
    volumes:
      - ./dags:/opt/airflow/dags
      - ./logs:/opt/airflow/logs
      - ./plugins:/opt/airflow/plugins
    ports:
      - "8081:8080"
    restart: unless-stopped
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  airflow-scheduler:
    image: apache/airflow:2.7.3
    container_name: airflow-scheduler
    command: scheduler
    depends_on:
      airflow-db:
        condition: service_healthy
      airflow-redis:
        condition: service_healthy
    environment:
      AIRFLOW__CORE__EXECUTOR: CeleryExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@airflow-db/airflow
      AIRFLOW__CELERY__RESULT_BACKEND: db+***********************************************
      AIRFLOW__CELERY__BROKER_URL: redis://:@airflow-redis:6379/0
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
      AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
      AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session'
      AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
    volumes:
      - ./dags:/opt/airflow/dags
      - ./logs:/opt/airflow/logs
      - ./plugins:/opt/airflow/plugins
    restart: unless-stopped
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD-SHELL", "airflow jobs check --job-type SchedulerJob --hostname \"$${HOSTNAME}\""]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================
  # MONITORING & VISUALIZATION (GRAFANA)
  # ============================================
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - warehouse-db
    networks:
      - data-pipeline
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

# ============================================
# VOLUMES & NETWORKS
# ============================================
volumes:
  hadoop_namenode:
  hadoop_datanode:
  hive_postgresql:
  nifi_database_repository:
  nifi_flowfile_repository:
  nifi_content_repository:
  nifi_provenance_repository:
  nifi_state:
  nifi_logs:
  nifi_conf:
  warehouse_data:
  airflow_db:
  grafana_data:

networks:
  data-pipeline:
    driver: bridge
