#!/usr/bin/env python3
"""
Airflow Connections Setup Script
This script creates the necessary connections for the E-commerce Data Pipeline.
Run this script after Airflow is initialized to set up connections.
"""

from airflow import settings
from airflow.models import Connection
from sqlalchemy.orm import sessionmaker
import logging

def create_connection(conn_id, conn_type, host=None, login=None, password=None, 
                     port=None, schema=None, extra=None):
    """Create or update an Airflow connection"""
    session = settings.Session()
    
    # Check if connection already exists
    existing_conn = session.query(Connection).filter(Connection.conn_id == conn_id).first()
    
    if existing_conn:
        logging.info(f"Connection {conn_id} already exists, updating...")
        existing_conn.conn_type = conn_type
        existing_conn.host = host
        existing_conn.login = login
        existing_conn.password = password
        existing_conn.port = port
        existing_conn.schema = schema
        existing_conn.extra = extra
    else:
        logging.info(f"Creating new connection: {conn_id}")
        new_conn = Connection(
            conn_id=conn_id,
            conn_type=conn_type,
            host=host,
            login=login,
            password=password,
            port=port,
            schema=schema,
            extra=extra
        )
        session.add(new_conn)
    
    session.commit()
    session.close()
    logging.info(f"Connection {conn_id} configured successfully")

def setup_all_connections():
    """Set up all required connections for the pipeline"""
    
    # PostgreSQL Warehouse Connection
    create_connection(
        conn_id='warehouse_postgres',
        conn_type='postgres',
        host='warehouse-db',
        login='warehouse_user',
        password='warehouse_pass',
        port=5432,
        schema='warehouse'
    )
    
    # HDFS Connection
    create_connection(
        conn_id='hdfs_default',
        conn_type='hdfs',
        host='namenode',
        port=9000,
        extra='{"use_ssl": false}'
    )
    
    # Spark Connection
    create_connection(
        conn_id='spark_default',
        conn_type='spark',
        host='spark://spark-master',
        port=7077,
        extra='{"deploy-mode": "client", "spark-home": "/opt/spark"}'
    )
    
    # NiFi HTTP Connection
    create_connection(
        conn_id='nifi_http',
        conn_type='http',
        host='nifi',
        port=8443,
        extra='{"verify": false, "timeout": 30}'
    )
    
    # Grafana HTTP Connection
    create_connection(
        conn_id='grafana_http',
        conn_type='http',
        host='grafana',
        port=3000,
        login='admin',
        password='admin'
    )
    
    logging.info("All connections have been configured successfully!")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    setup_all_connections()
