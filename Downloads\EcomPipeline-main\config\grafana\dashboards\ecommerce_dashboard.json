{"dashboard": {"id": null, "title": "E-commerce Marketing Insights", "tags": ["ecommerce", "marketing", "analytics"], "timezone": "browser", "panels": [{"id": 1, "title": "Revenue by Payment Method", "type": "piechart", "targets": [{"expr": "", "format": "table", "rawSql": "SELECT \n  CASE \n    WHEN pay_method = 0 THEN 'Credit Card'\n    WHEN pay_method = 1 THEN 'Debit Card'\n    WHEN pay_method = 2 THEN 'PayPal'\n    WHEN pay_method = 3 THEN 'Bank Transfer'\n    ELSE 'Other'\n  END as payment_method,\n  total_revenue\nFROM revenue_by_paymethod\nORDER BY total_revenue DESC", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "options": {"pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "visible", "placement": "right"}}}, {"id": 2, "title": "Newsletter Subscription Impact", "type": "bargauge", "targets": [{"expr": "", "format": "table", "rawSql": "SELECT \n  CASE \n    WHEN newsletter = 0 THEN 'Not Subscribed'\n    WHEN newsletter = 1 THEN 'Subscribed'\n    ELSE 'Unknown'\n  END as newsletter_status,\n  num_customers,\n  avg_revenue\nFROM newsletter_stats\nORDER BY newsletter DESC", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "options": {"orientation": "horizontal", "displayMode": "gradient"}}, {"id": 3, "title": "Browser Performance Metrics", "type": "table", "targets": [{"expr": "", "format": "table", "rawSql": "SELECT \n  CASE \n    WHEN browser = 0 THEN 'Chrome'\n    WHEN browser = 1 THEN 'Firefox'\n    WHEN browser = 2 THEN 'Safari'\n    WHEN browser = 3 THEN 'Edge'\n    ELSE 'Other'\n  END as browser_name,\n  ROUND(avg_time_spent::numeric, 2) as avg_time_spent_minutes,\n  ROUND(avg_purchase_value::numeric, 2) as avg_purchase_value_usd\nFROM browser_stats\nORDER BY avg_purchase_value DESC", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "options": {"showHeader": true}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}}}, {"id": 4, "title": "Average Time Spent by <PERSON><PERSON><PERSON>", "type": "stat", "targets": [{"expr": "", "format": "table", "rawSql": "SELECT \n  CASE \n    WHEN browser = 0 THEN 'Chrome'\n    WHEN browser = 1 THEN 'Firefox'\n    WHEN browser = 2 THEN 'Safari'\n    WHEN browser = 3 THEN 'Edge'\n    ELSE 'Other'\n  END as browser_name,\n  avg_time_spent\nFROM browser_stats\nORDER BY avg_time_spent DESC", "refId": "A"}], "gridPos": {"h": 6, "w": 12, "x": 0, "y": 16}, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto"}}, {"id": 5, "title": "Average Purchase Value by <PERSON><PERSON><PERSON>", "type": "stat", "targets": [{"expr": "", "format": "table", "rawSql": "SELECT \n  CASE \n    WHEN browser = 0 THEN 'Chrome'\n    WHEN browser = 1 THEN 'Firefox'\n    WHEN browser = 2 THEN 'Safari'\n    WHEN browser = 3 THEN 'Edge'\n    ELSE 'Other'\n  END as browser_name,\n  avg_purchase_value\nFROM browser_stats\nORDER BY avg_purchase_value DESC", "refId": "A"}], "gridPos": {"h": 6, "w": 12, "x": 12, "y": 16}, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto"}}], "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "5m", "schemaVersion": 27, "version": 1, "links": []}}