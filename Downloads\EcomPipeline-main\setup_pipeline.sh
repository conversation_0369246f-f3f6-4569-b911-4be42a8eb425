#!/bin/bash

# E-commerce Data Pipeline Setup Script
# This script sets up the complete data pipeline infrastructure

set -e  # Exit on any error

echo "🚀 Starting E-commerce Data Pipeline Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON><PERSON> and <PERSON>er Compose are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs/dag_processor_manager
    mkdir -p logs/scheduler
    mkdir -p data/input
    mkdir -p data/output
    mkdir -p config/grafana/provisioning/datasources
    mkdir -p config/grafana/provisioning/dashboards
    mkdir -p config/grafana/dashboards
    mkdir -p plugins
    
    print_success "Directories created"
}

# Initialize Airflow database
init_airflow() {
    print_status "Initializing Airflow database..."
    
    # Start only the database services first
    docker-compose up -d airflow-db airflow-redis
    
    # Wait for database to be ready
    sleep 10
    
    # Initialize Airflow database
    docker-compose run --rm airflow-webserver airflow db init
    
    # Create admin user
    docker-compose run --rm airflow-webserver airflow users create \
        --username admin \
        --firstname Admin \
        --lastname User \
        --role Admin \
        --email <EMAIL> \
        --password admin
    
    print_success "Airflow initialized"
}

# Setup Airflow connections
setup_airflow_connections() {
    print_status "Setting up Airflow connections..."
    
    # Copy the connection setup script to the container and run it
    docker-compose exec airflow-webserver python /opt/airflow/dags/../config/airflow_connections.py
    
    print_success "Airflow connections configured"
}

# Start all services
start_services() {
    print_status "Starting all services..."
    
    # Start all services
    docker-compose up -d
    
    print_success "All services started"
}

# Wait for services to be healthy
wait_for_services() {
    print_status "Waiting for services to be healthy..."
    
    services=("namenode" "datanode" "spark-master" "nifi" "warehouse-db" "airflow-webserver" "grafana")
    
    for service in "${services[@]}"; do
        print_status "Waiting for $service to be healthy..."
        timeout=300  # 5 minutes timeout
        elapsed=0
        
        while [ $elapsed -lt $timeout ]; do
            if docker-compose ps $service | grep -q "healthy\|Up"; then
                print_success "$service is ready"
                break
            fi
            sleep 10
            elapsed=$((elapsed + 10))
        done
        
        if [ $elapsed -ge $timeout ]; then
            print_warning "$service might not be fully ready, but continuing..."
        fi
    done
}

# Create initial HDFS directories
setup_hdfs() {
    print_status "Setting up HDFS directories..."
    
    # Wait a bit more for HDFS to be fully ready
    sleep 30
    
    # Create HDFS directories
    docker-compose exec namenode hdfs dfs -mkdir -p /user/marketing/datasets
    docker-compose exec namenode hdfs dfs -mkdir -p /user/marketing/processed
    docker-compose exec namenode hdfs dfs -mkdir -p /user/marketing/archive
    
    # Upload initial dataset
    docker-compose exec namenode hdfs dfs -put -f /data/dataset.csv /user/marketing/datasets/dataset.csv
    
    print_success "HDFS setup completed"
}

# Create PostgreSQL warehouse tables
setup_warehouse() {
    print_status "Setting up PostgreSQL warehouse..."
    
    # Create tables for Spark output
    docker-compose exec warehouse-db psql -U warehouse_user -d warehouse -c "
    CREATE TABLE IF NOT EXISTS revenue_by_paymethod (
        pay_method INTEGER,
        total_revenue DOUBLE PRECISION
    );
    
    CREATE TABLE IF NOT EXISTS newsletter_stats (
        newsletter INTEGER,
        num_customers BIGINT,
        avg_revenue DOUBLE PRECISION
    );
    
    CREATE TABLE IF NOT EXISTS browser_stats (
        browser INTEGER,
        avg_time_spent DOUBLE PRECISION,
        avg_purchase_value DOUBLE PRECISION
    );
    "
    
    print_success "PostgreSQL warehouse setup completed"
}

# Display service URLs
display_urls() {
    print_success "🎉 Pipeline setup completed successfully!"
    echo ""
    echo "📊 Service URLs:"
    echo "  • Airflow Web UI:     http://localhost:8081 (admin/admin)"
    echo "  • NiFi Web UI:        https://localhost:8443 (admin/admin123456789)"
    echo "  • Spark Master UI:    http://localhost:8080"
    echo "  • HDFS NameNode UI:   http://localhost:9870"
    echo "  • Grafana:            http://localhost:3000 (admin/admin)"
    echo "  • PostgreSQL:         localhost:5433 (warehouse_user/warehouse_pass)"
    echo ""
    echo "🔧 Next Steps:"
    echo "  1. Access Airflow at http://localhost:8081"
    echo "  2. Enable the 'ecom_data_pipeline' DAG"
    echo "  3. Configure NiFi processors for data ingestion"
    echo "  4. Create Grafana dashboards for visualization"
    echo ""
    echo "📝 To run the pipeline manually:"
    echo "  docker-compose exec airflow-webserver airflow dags trigger ecom_data_pipeline"
}

# Main execution
main() {
    check_prerequisites
    create_directories
    
    print_status "Starting Docker services..."
    start_services
    
    wait_for_services
    init_airflow
    setup_airflow_connections
    setup_hdfs
    setup_warehouse
    
    display_urls
}

# Run main function
main
